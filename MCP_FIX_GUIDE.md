# MCP Feedback Enhanced - "Request Cancelled" 错误修复指南

## 🎯 问题描述

在macOS bash环境下使用MCP Feedback Enhanced时，会出现以下错误：

```
✖ MCP ERROR (mcp-feedback-enhanced): Error: Received a response for an unknown message ID:
   {"jsonrpc":"2.0","id":2,"error":{"code":0,"message":"Request cancelled"}}
```

## 🔍 问题根源

WebSocket连接建立后，服务器会自动发送多个消息：
1. `connection_established` - 连接确认消息
2. `session_updated` - 会话更新消息  
3. `status_update` - 状态更新消息

这些消息被FastMCP误认为是对原始MCP请求的响应，但格式不匹配，导致"unknown message ID"错误。

## 🛠️ 修复方案

### 方案1: 使用修复后的本地版本（推荐）

1. **克隆并修复项目**：
```bash
git clone https://github.com/Minidoracat/mcp-feedback-enhanced.git
cd mcp-feedback-enhanced
uv sync --dev
```

2. **应用修复**（已在本地版本中完成）：
   - 添加环境变量控制：`MCP_DISABLE_CONNECTION_MESSAGE=true`
   - 禁用所有WebSocket自动消息发送
   - 保持功能完整性，只在用户交互时发送消息

3. **配置MCP使用本地版本**：
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uv",
      "args": ["run", "python", "-m", "mcp_feedback_enhanced"],
      "cwd": "/Volumes/ExternalData/mcp-feedback-enhanced",
      "timeout": 1800,
      "env": {
        "MCP_DISABLE_CONNECTION_MESSAGE": "true",
        "MCP_DESKTOP_MODE": "true",
        "MCP_DEBUG": "false",
        "MCP_WEB_PORT": "8765"
      },
      "autoApprove": ["interactive_feedback", "get_system_info"]
    }
  }
}
```

### 方案2: 等待官方修复

等待项目维护者合并修复到官方版本，然后使用：
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 1800,
      "env": {
        "MCP_DISABLE_CONNECTION_MESSAGE": "true",
        "MCP_DESKTOP_MODE": "true"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

## 🧪 测试验证

1. **测试基本功能**：
```bash
MCP_DEBUG=true MCP_DISABLE_CONNECTION_MESSAGE=true uv run python -m mcp_feedback_enhanced test
```

2. **测试Web UI**：
```bash
MCP_DEBUG=true MCP_DISABLE_CONNECTION_MESSAGE=true uv run python -m mcp_feedback_enhanced test --web
```

3. **测试桌面应用**：
```bash
MCP_DEBUG=true MCP_DISABLE_CONNECTION_MESSAGE=true MCP_DESKTOP_MODE=true uv run python -m mcp_feedback_enhanced test --desktop
```

## ✅ 预期效果

修复后应该看到：
- ✅ 界面正常弹出
- ✅ WebSocket连接稳定
- ✅ 日志显示："已禁用所有 WebSocket 自動消息"
- ✅ 不再出现 "Request cancelled" 错误
- ✅ 用户反馈功能完全正常

## 🔧 技术细节

### 修复的代码位置
- 文件：`src/mcp_feedback_enhanced/web/routes/main_routes.py`
- 行数：280-325
- 修改：添加环境变量控制，禁用自动消息发送

### 关键环境变量
- `MCP_DISABLE_CONNECTION_MESSAGE=true` - 禁用自动消息（修复核心）
- `MCP_DESKTOP_MODE=true` - 使用桌面模式（提升性能）
- `MCP_DEBUG=false` - 减少日志输出（提升性能）

## 📞 支持

如果修复后仍有问题，请提供：
1. 完整的错误日志
2. MCP配置文件
3. 系统环境信息（macOS版本、Python版本等）
